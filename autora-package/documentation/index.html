<!DOCTYPE html>
<!--[if IE 8 ]><html class="ie" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-US" lang="en-US"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!--><html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-US" lang="en-US"><!--<![endif]-->
<head>
   <!-- Basic Page Needs -->
   <meta charset="utf-8">
    <!--[if IE]><meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'><![endif]-->
   <title>Autora - Construction Business HTMl Template</title>
   <meta name="description" content="">
   <meta name="keywords" content="">
   <meta name="author" content="">

	<link rel="stylesheet" href="css/prism.css"> 
	<link rel="stylesheet" href="css/style.css">

	<script src="javascript/jquery.min.js"></script>
	<script src="javascript/jquery.easing.1.3.min.js"></script>
	<script src="javascript/jquery.nav.js"></script>
	<script src="javascript/jquery.scrollTo.js"></script>
	<script src="javascript/prism.js"></script>
	<script src="javascript/main.js"></script>

	<!--[if lt IE 9]>
	<script src="javascript/html5.js"></script>
	<![endif]-->
  
	<link rel="shortcut icon" href="favicon/favicon.png">
</head>
<body>

	<div id="sidebar">
		<div id="logo">
			<a href="#top">
				<img src="images/logo.png" alt="logo" />
			</a>
		</div>
		<nav id="nav">
			<ul>
				<li class="current"><a href="#Welcome">Welcome</a></li>
				<li><a href="#HTML">HTML Structure</a></li>
				<li><a href="#CSS">CSS System</a></li>
				<li><a href="#Javascript">Javascript Custom</a></li>
				<li><a href="#Shortcodes">Shortcodes System</a></li>
				<li><a href="#Photoshop">Photshop Files Included</a></li>
				<li><a href="#Credits">Credits</a></li>
			</ul>
		</nav>
	</div><!-- /.sidebar -->

	<div id="main-content">
		<div class="section" id="Welcome">			
			<h3>Autora - Construction Business HTMl Template
          </h3>

			<div class="content">
				<div class="information">
					<ul>
						<li>Version: 1.0.0</li>
						<li>Last Updated: 07/02/2018</li>
						<li>Author: <a href="https://themeforest.net/user/themesflat">themesflat</a></li>
					</ul>
					<div class="message">
					Thank you so much for your interests. Your comments and ratings would be much appreciated. 

					If you purchase this template, you will get support. We will update this template time by time and we want to hear your wishes for the future updates or for complete new templates.

					You need file PSD or images please contact via skyper:themesflat <NAME_EMAIL> we will send this for you.
					
					</div>
				</div>
				<h3>Template features:</h3>
				<ul>
					<li>HTML5 &amp; CSS3</li>	
					<li>Responsive Template</li>
					<li>Free icons used</li>
					<li>Pixel Perfect</li>
					<li>Clean &amp; Unique Design</li>
					<li>Easy to customize</li>
					<li>Retina Ready</li>
					<li>Unlimited Colors</li>
					<li>Boxed or Wide layout</li>	
					<li>Ajax Contact Form</li>
					<li>2 Home Page </li>	
					<li>2 Portfolio page </li>
					<li>Parallax Effect</li>	
					<li>&amp; much more...</li>
				</ul>
			</div>
		</div><!-- /.section -->

		<div class="section" id="HTML">
			<h3>HTML Structure</h3>
			<div class="content"><strong> Autora - Construction Business HTMl Template </strong> 
				<h4>Below is Autora basic structure:</h4>

				<pre><code class="language-markup">&lt;!DOCTYPE html&gt;
&lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;title&gt;Autora Template&lt;/title&gt;

    &lt;!-- Mobile Specific Metas --&gt;

    &lt;!-- Bootstrap  --&gt;

    &lt;!-- Theme Style --&gt;

    &lt;!-- Responsive --&gt;

    &lt;!-- colors --&gt;    
   
    &lt;!-- Animation Style --&gt;    

    &lt;!-- Favicon and touch icons  --&gt;
&lt;/head&gt;

&lt;body&gt;
	&lt;!-- Header --&gt;	

	&lt;!-- Slider --&gt;

    &lt;!-- Section --&gt;
    &lt;section class=&quot;flat-row&quot;&gt; &lt;/section&gt;

    &lt;!-- Footer --&gt;
    &lt;footer class=&quot;footer&quot;&gt; &lt;/footer&gt;

   	&lt;!-- Bottom --&gt;
   	&lt;div class=&quot;bottom&quot;&gt; &lt;/bottom&gt;

   	&lt;!-- Go Top --&gt;
    &lt;a class=&quot;go-top&quot;&gt;
     	&lt;i class=&quot;fa fa-angle-up&quot;&gt;&lt;/a&gt;
    &lt;/a&gt;
   
   &lt;!-- Javascript --&gt;
   &lt;script type=&quot;text/javascript&quot; src=&quot;assets/js/jquery.min.js&quot;&gt;&lt;/script&gt;
   &lt;script type=&quot;text/javascript&quot; src=&quot;assets/js/bootstrap.min.js&quot;&gt;&lt;/script&gt;
   &lt;script type=&quot;text/javascript&quot; src=&quot;assets/js/jquery.easing.js&quot;&gt;&lt;/script&gt;
   &lt;script type=&quot;text/javascript&quot; src=&quot;assets/js/jquery-waypoints.js&quot;&gt;&lt;/script&gt;
   &lt;script type=&quot;text/javascript&quot; src=&quot;assets/js/owl.carousel.js&quot;&gt;&lt;/script&gt;
   &lt;script type=&quot;text/javascript&quot; src=&quot;assets/js/parallax.js&quot;&gt;&lt;/script&gt;
   &lt;script type=&quot;text/javascript&quot; src=&quot;assets/js/jquery.cookie.js&quot;&gt;&lt;/script&gt;   
   &lt;script type=&quot;text/javascript&quot; src=&quot;assets/js/switcher.js&quot;&gt;&lt;/script&gt;      
   &lt;script type=&quot;text/javascript&quot; src=&quot;assets/js/main.js&quot;&gt;&lt;/script&gt;
&lt;/body&gt;

&lt;/html&gt;</code></pre>

			</div><!-- /.content -->
		</div><!-- /.section -->

		<div class="section" id="CSS">
			<h3>CSS System</h3>
			<div class="content">
				The Autora CSS System contains 3 parts: 
				<ul>
					<li>Template style</li>
					<li>Shortcodes style</li>
					<li>Responsive style</li>
					<li>Javascript - Fonts - Animation - Bootstrap style</li>
				</ul>
				<h4>Style.css ( Template style )</h4>
				<pre><code class="language-markup">
/**
  *	Reset Browsers
  * General
  * Elements
  * Forms
  * Typography
  * Extra classes
  * Layouts
  * Column
  * Effect
  * Rev Slider
  * Top Bar
  * Header
  * Featured Title
  * Blog Post
  * Widgets
  * Pagination
  * Footer
  * Bottom
  * Scroll Top
  * Switcher
  * Boxed
  * Media Queries
*/
				</code></pre>

				<h4>Shortcodes.css ( Shortcodes - Elements style )</h4>
<p>You can easily use the shortcodes to create your own page.</p>
				<pre><code class="language-markup">
/**
  *	Button
  * Headings
  * Icon Box
  * Half Background
  * List
  * Image box
  * Carousel Control
  * Carousel thumb
  * Parallax
  * Project
  * Testimonials
  * Quote
  * Tabs
  * Accordions
  * Partner
  * Progress Bar
  * Request
  * Contact Form 7
  * Filter
  * Maps
  * Pricing
  * Counter
  * Team
  * Content wrap
  * Gallery
  * Project detail
  * Lines
*/
				</code></pre>

<h4>Javascript - Fonts - Animation - Bootstrap style</h4>
				<pre><code class="language-markup">/**  
  * font-awesome.css

  * owl.carousel.css

  * animate.css
  * shortcodes.css

  * bootstrap.css 
  * revolution-slider.css
*/
				</code></pre>

			</div><!-- /.content -->
		</div><!-- /.section -->

		<div class="section" id="Javascript">
			<h3>Javascript Custom</h3>
			<div class="content">
				Here you can modify any settings for ( Retina Logos, Animations, Testimonials, Sliders, &amp; Progress bar and more..) 
				<h4>Main.js ( js/main.js )</h4>
				<pre><code class="language-markup">
/**
  * PreLoader
  * Retina Logos
  * Header Fixed
  * Mobile Navigation
  * Scroll to Top  
*/

				</code></pre>

			</div><!-- /.content -->
		</div><!-- /.section -->

		<div class="section" id="Shortcodes">
			<h3>Shortcodes System</h3>
			<div class="content">
			<p>You can easily use the shortcodes to create your own page.</p>
				<pre><code class="language-markup">    
/**
  *	Button
  * Headings
  * Icon Box
  * Half Background
  * List
  * Image box
  * Carousel Control
  * Carousel thumb
  * Parallax
  * Project
  * Testimonials
  * Quote
  * Tabs
  * Accordions
  * Partner
  * Progress Bar
  * Request
  * Contact Form 7
  * Filter
  * Maps
  * Pricing
  * Counter
  * Team
  * Content wrap
  * Gallery
  * Project detail
  * Lines
*/
				</code></pre>


			</div><!-- /.content -->
		</div><!-- /.section -->
		
		

		<div class="section" id="Credits">
			<h3>Credits</h3>
			<div class="content">
				<ul>
				    <li>
						Photos
						<ul>
							<li>All images are just used for preview purpose only and NOT included in the final purchase files.</li>
						</ul>
					</li>
					<li>
						Fonts &amp; Icons
						<ul>			
							<li><a href="http://fortawesome.github.io/Font-Awesome/" target="blank">Font-Awesome</a></li>
							
							<li><a href="https://fonts.google.com/specimen/Open+Sans" target="blank">Open Sans</a> is available for free on Google fonts.</li>
							
						</ul>
					</li>
					<li>
						Javascript
						<ul>
							<li>jQuery (<a href="http://jquery.com/" target="_blank">http://jquery.com/</a>)</li>
							<li>Bootstrap (<a href="http://getbootstrap.com" target="_blank">http://getbootstrap.com</a>)</li>
							<li>Easing (<a href="http://gsgd.co.uk/sandbox/jquery/easing/" target="_blank">http://gsgd.co.uk/sandbox/jquery/easing/</a>)</li>							
							<li>owl.carousel (<a href="http://owlgraphic.com/owlcarousel/" target="_blank">http://owlgraphic.com/owlcarousel/</a>)</li>
							<li>WayPoints (<a href="http://imakewebthings.com/jquery-waypoints/" target="_blank">http://imakewebthings.com/jquery-waypoints/</a>)</li>			
							<li>Parallax (<a href="http://www.ianlunn.co.uk/plugins/jquery-parallax/" target="_blank">http://www.ianlunn.co.uk/plugins/jquery-parallax/</a>)</li>
							<li>Cookie (<a href="https://github.com/carhartl/jquery-cookie/" target="_blank">https://github.com/carhartl/jquery-cookie</a>)</li>
						</ul>
					</li>
				</ul>
			</div><!-- /.content -->
		</div><!-- /.section -->

		<div style="height: 1000px;"></div>
		</div><!-- /#main-content -->
</body>

</html>