/**
  *	Button
  * Headings
  * Icon Box
  * Half Background
  * List
  * Image box
  * Carousel Control
  * Carousel thumb
  * Parallax
  * Project
  * Testimonials
  * Quote
  * Tabs
  * Accordions
  * Partner
  * Progress Bar
  * Request
  * Contact Form 7
  * Filter
  * Maps
  * Pricing
  * Counter
  * Team
  * Content wrap
  * Gallery
  * Project detail
  * Lines
*/

/* Button
-------------------------------------------------------------- */
.themesflat-button { display: inline-block; font-size: 14px;  font-weight: 600; color: #fff; background-color: transparent; padding: 14px 40px;  font-family: 'Montserrat', sans-serif; letter-spacing: 0.5px;  position: relative; z-index: 1; overflow: hidden; }
.themesflat-button.font-default { font-family: 'Open Sans', sans-serif; font-size: 14px; font-weight: 400; padding: 13px 25px; letter-spacing: 0px; }
.themesflat-button.bg-accent { background-color: #ffc30c; }
.themesflat-button.bg-white { background-color: #fff; color: #ffc30c; }
.themesflat-button.bg-light-white { background-color: #f2f2f2; color: #555; }
.themesflat-button.bg-white.color-333 { background-color: #fff; color: #333; }
.themesflat-button.big { padding: 15px 36px; }
.themesflat-button.small { color: #444; padding: 14px 26px; }
.themesflat-button.pd30 { padding: 11px 30px; }

.themesflat-button:before { content: ""; position: absolute; width: 100%; height: 0%; z-index: -1; top: 50%; left: 50%; opacity: 0; visibility: hidden; -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; -webkit-transform: translate(-50%,-50%); -moz-transform: translate(-50%,-50%) ; -ms-transform: translate(-50%,-50%); -o-transform: translate(-50%,-50%); transform: translate(-50%,-50%); }

.themesflat-button:hover:before { opacity: 1; visibility: visible; height: 100%; width: 110%;}
.themesflat-button.bg-white.color-333:hover:before,
.themesflat-button.bg-accent:hover:before { background-color: #212121;}
.themesflat-button.bg-light-white:hover:before,
.themesflat-button.bg-white:hover:before { background-color: #ffc30c; }

.themesflat-button.bg-light-white:hover,
.themesflat-button.bg-white:hover,
.themesflat-button.bg-accent:hover { color: #fff; }

/* Button custom */
.button-wrap.has-icon .themesflat-button > span { position: relative; display: block; }
.button-wrap.has-icon .themesflat-button > span > .icon { position: absolute; top: 50%; transform: translateY(-50%); font-size: 25px; }
.button-wrap.has-icon.size-14 .themesflat-button > span > .icon { font-size: 14px; }

.button-wrap.has-icon.icon-left .themesflat-button > span > .icon { left: 0; }
.button-wrap.has-icon.icon-left .themesflat-button > span { padding-left: 34px; }
.button-wrap.has-icon.icon-left.pf21 .themesflat-button > span { padding-left: 21px; }

.button-wrap.has-icon.icon-right .themesflat-button > span > .icon { right: 0; }

.themesflat-button.w100 { width: 100%; }
.themesflat-button.pd26 {  padding: 14px 26px; }
.themesflat-button.pd32 {  padding: 14px 32px; }

/* Headings
-------------------------------------------------------------- */
.themesflat-headings .heading,
.themesflat-headings .sub-heading {  margin: 0px; }
.themesflat-headings .sup-heading { font-family: 'Montserrat', sans-serif; }
.themesflat-headings .sub-heading { font-weight: 300; color: #666; }
.themesflat-headings.text-center .sub-heading { text-align: center; margin: 0 auto; max-width: 590px;  }
.themesflat-headings .sep-icon { display: inline-block; position: relative; }

.themesflat-headings .sep-icon-after,
.themesflat-headings .sep-icon-before { display: block; position: absolute; top: 50%; border-bottom: 1px solid rgba(0,0,0,0.1); right: 100%; width: 9999px;  }
.themesflat-headings .sep-icon-after { right: auto; left: 100%; }
.themesflat-headings .sep.has-icon { display: block; height: auto; margin: 0 auto; overflow: hidden; }
.themesflat-headings .sep.has-icon .icon-wrap { display: inline-block; padding: 0px 8px; font-size: 22px;  }
.themesflat-headings .sep.has-icon .icon-wrap > i { color: #ffc30c; }

.themesflat-headings .sep.width-125 { width: 125px; }
.themesflat-testimonials .sep.has-width.w80,
.themesflat-headings .sep.has-width.w80 { width: 80px; height: 2px; }
.themesflat-headings .sep.has-width.w60 { width: 60px; height: 2px; }
.themesflat-testimonials .sep.has-width.accent-bg,
.themesflat-headings .sep.has-width.accent-bg { background-color: #ffc30c; }

.themesflat-headings .sep.border-color-light .sep-icon-after,
.themesflat-headings .sep.border-color-light .sep-icon-before { border-color: #777; }

/* Style 1 */
.themesflat-headings.style-1 .heading { font-size: 28px; line-height: 58px;}
.themesflat-headings.style-1 .sub-heading { font-size: 16px; line-height: 32px; margin-top: 16px;  }

/* Style 2 */
.themesflat-headings.style-2 .heading { font-size: 22px; line-height: 58px; }
.themesflat-headings.style-2 .sub-heading { font-size: 14px; line-height: 22px; font-weight: 400; color: #555; margin-top: 29px; }
.themesflat-headings.style-2 .sup-heading { font-size: 16px; color: #222;  }

/* Font hind */
.themesflat-headings.font-hind .sub-heading { font-size: 17px; line-height: 28px; margin-top: 13px; color: #808080; font-family: 'Hind', sans-serif; font-weight: 400; letter-spacing: -0.4px; max-width: 100%; }

/* Icon Box
-------------------------------------------------------------- */
.themesflat-icon-box .heading,
.themesflat-icon-box .sub-heading { margin: 0px; }
.themesflat-icon-box .sep { width: 60px; height: 2px; background-color: #e7e7e7; }

.themesflat-icon-box .icon-wrap { font-size: 45px; line-height: 57px; -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; }

/* Has width */
.themesflat-icon-box.has-width.w95 .icon-wrap { width: 95px; height: 95px; line-height: 95px; font-size: 45px; }

/* Circle */
.themesflat-icon-box.circle .icon-wrap { border-radius: 50%; }

/* Background */
.themesflat-icon-box.light-bg .icon-wrap { background-color: #f5f5f5;  position: relative; z-index: 1; }

/* Color */
.themesflat-icon-box.accent-color .icon-wrap { color: #ffc30c; }

/* Border */
.themesflat-icon-box.border-light { border: 1px solid #f2f2f2; }

/* Center */
.themesflat-icon-box.align-center { text-align: center; }
.themesflat-icon-box.align-center  .icon-wrap { margin: 0 auto; }

.themesflat-icon-box.align-center .sep { margin: 0 auto; }

/* Padding inner */
.themesflat-icon-box.padding-inner { padding: 38px 15px 29px 15px; }

.themesflat-icon-box.padding-inner.pd33 { padding: 33px 15px 25px 15px; }

/*  Icon top */
.themesflat-icon-box.icon-top .icon-wrap { position: relative; }

/* Icon left */
.themesflat-icon-box.icon-left { position: relative; }
.themesflat-icon-box.icon-left .icon-wrap { position: absolute; top: 0; left: 0; }
.themesflat-icon-box.icon-left .text-wrap { padding-left: 80px; }

/* Style 1 */
.themesflat-icon-box.style-1 .heading { font-size: 18px; line-height: 28px; margin: 26px 0px 13px 0px; }
.themesflat-icon-box.style-1 .sub-heading { color: #666; line-height: 26px; margin-top: 19px; }

.themesflat-icon-box.style-1.light-bg .icon-wrap:before { content: ""; width: 100%; height: 100%; border-radius: 50%; background-color: #ffc30c; position: absolute; top: 0; left: 0; z-index: -1; transform: scale(0.7); -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; opacity: 0; visibility: hidden; }

.themesflat-icon-box.style-1.light-bg:hover .icon-wrap:before { transform: scale(1);  opacity: 1; visibility: visible;}

.themesflat-icon-box.style-1.accent-color:hover .icon-wrap { color: #fff; }

/* Style 2 */
.themesflat-icon-box.style-2 .heading { font-size: 16px; line-height: 26px; margin: 0px; }
.themesflat-icon-box.style-2 .sub-heading { line-height: 26px; letter-spacing: 0.1px; margin-top: 15px;}

.themesflat-icon-box.style-2.accent-color:hover .icon-wrap { color: #222; }

.themesflat-icon-box.bg-white-column { -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; }
.themesflat-icon-box.bg-white-column:hover { box-shadow: 1px 1px 10px 0px rgba(0, 0, 0, 0.2); }

/* Style 3 */
.themesflat-icon-box.style-3 { padding: 23px 0px 38px 0px; }
.themesflat-icon-box.style-3 .icon-wrap { font-size: 40px; line-height: 80px; }
.themesflat-icon-box.style-3 .heading { font-size: 16px; line-height: 30px; margin-top: 5px;  }
.themesflat-icon-box.style-3 .sub-heading { line-height: 22px; margin-top: 6px; }
.themesflat-icon-box.style-3 .more-link { display: block; line-height: 22px; color: #555; }
.themesflat-icon-box.style-3 .more-link a { color: #555; position: relative; }
.themesflat-icon-box.style-3 .more-link a:after { content: ""; position: absolute; bottom: 0; left: 0; width: 100%; height: 2px; background-color: #c0c0c0; -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; }

.themesflat-icon-box.style-3 .more-link a:hover { color: #ffc30c; }
.themesflat-icon-box.style-3 .more-link a:hover:after { width: 0; }

@media only screen and (max-width: 1199px) {
	.themesflat-icon-box.padding-inner { padding: 15px; }
  .themesflat-icon-box.padding-inner.pd33 { padding: 10px; }
  .themesflat-icon-box.icon-left .text-wrap { padding-left: 62px; }
  .themesflat-icon-box.style-2 .heading { font-size: 14px; }
}

@media only screen and (max-width: 991px) {
  .themesflat-icon-box.style-3 .heading { font-size: 15px;  }
}

/* Half Background
-------------------------------------------------------------- */
.half-background { background-size: cover; background-repeat: no-repeat; background-position: center center; overflow: hidden; }

/* Style */
.half-background.style-1 { background-image: url('../img/gallery/gallery-1-960x560.jpg'); }
.half-background.style-2 { background-image: url('../img/bg-services-1-420x330.jpg'); }

@media only screen and (max-width: 991px) {
	.half-background.style-2 { height: 330px; }
}

@media only screen and (max-width: 767px) {
	.half-background.style-1 { height: 330px; }
	.half-background.style-2 { margin-top: 35px; }
}
/* List
-------------------------------------------------------------- */
.themesflat-list .inner { padding: 4px 0px; }
.themesflat-list .item { display: block; position: relative; }
.themesflat-list .icon { color: #999; }
.themesflat-list.has-icon .icon { display: inline-block; position: absolute; top: 50%; left: 0; transform: translateY(-50%); } 

.themesflat-list.has-icon.align-top .icon { top: 0; transform: translateY(0%);}

/* Icon left */
.themesflat-list.icon-left .item { padding-left: 15px; }

/* Font size */
.themesflat-list.has-icon.size-16 .icon { font-size: 16px;}

/* Style 1 */
.themesflat-list.style-1 .icon { color: #ffc30c; }
.themesflat-list.style-1.icon-left .item { padding-left: 24px; width: 100%; }
.themesflat-list.style-1.icon-left.ofset-padding-32 .item { padding-left: 32px; }
.themesflat-list.style-1 .text { color: #666; }

/* Style 2 */
.themesflat-list.style-2 .text,
.themesflat-list.style-2 .icon { color: #777; }
.themesflat-list.style-2.icon-left .item { padding-left: 24px; }
.themesflat-list.style-2 .inner { padding: 0px; }

/* Image box
-------------------------------------------------------------- */
.themesflat-image-box .text-wrap { position: relative; }

/* Has icon */
.themesflat-image-box.has-icon .icon-wrap { display: inline-block; text-align: center; cursor: pointer;  }
.themesflat-image-box.has-icon.w65 .icon-wrap { width: 65px; height: 65px; height: 100%; background-color: #e2e2ea; line-height: 65px; font-size: 20px; color: #666; -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; }
.themesflat-image-box.has-icon.icon-right .icon-wrap { position: absolute; top: 0; right: 0;  }

.themesflat-image-box.has-icon:hover .icon-wrap { background-color: #ffc30c; color: #fff;  }

/* Style 1 */
.themesflat-image-box.style-1 .text-wrap {  background-color: #f5f5f7; }
.themesflat-image-box.style-1 .heading { font-size: 16px; font-weight: 500; margin: 0px; line-height: 65px;  transform: translateX(21px);  }

/* Style 2 */
.themesflat-image-box.style-2 .text-wrap { padding: 28px 8px 28px 19px }
.themesflat-image-box.style-2 .heading { font-size: 16px; line-height: 26px; color: #222; font-weight: 600; margin: 0px; }
.themesflat-image-box.style-2 p { color: #777; margin: 15px 0px 0px 0px; line-height: 26px; letter-spacing: -0.1px; }
.themesflat-image-box.style-2 .elm-readmore a {  font-family: 'Montserrat', sans-serif; font-weight: 500; color: #ffc30c; display: inline-block; margin-top: 10px; position: relative; }

.themesflat-image-box.style-2 .elm-readmore a:hover { transform: translateX(10px); }

@media only screen and (max-width: 1199px){
  .themesflat-image-box.style-2 .text-wrap { padding: 15px 0px; }
}

/* Carousel Control
-------------------------------------------------------------- */
.owl-theme .owl-nav, .owl-theme .owl-dots { display: none; }
.has-bullets .owl-theme .owl-dots { display: block; padding-top: 35px; }
.has-bullets.bullet24 .owl-theme .owl-dots { padding-top: 24px; }
.has-arrows .owl-theme .owl-nav { display: block; }
.has-arrows .owl-theme .owl-nav [class*='owl-'] { position: absolute; }
.has-arrows .owl-theme .owl-nav .owl-next { right: 0; }

/* Bullets Style */
.owl-theme .owl-dots { text-align: center; line-height: 10px; }
.owl-theme .owl-dots .owl-dot { display: inline-block; }
.owl-theme .owl-dots .owl-dot span { background-color: transparent; display: block; width: 10px; height: 10px; margin: 0 5px; border: 2px solid #ddd;  -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease;  }
.owl-theme .owl-dots .owl-dot:hover span,
.owl-theme .owl-dots .owl-dot.active span { background-color: #ffc30c; border-color:  #ffc30c;  }

/* Arrows Center */
.has-arrows.arrow-center .owl-theme .owl-nav [class*='owl-'] { top: 50%; }
.has-arrows.arrow-center.offset-v-24 .owl-theme .owl-nav [class*='owl-'] { margin-top: -24px; }
.has-arrows.arrow-center.offset-v-60 .owl-theme .owl-nav [class*='owl-'] { margin-top: -60px; }
.has-arrows.arrow-center.offset-v-82 .owl-theme .owl-nav [class*='owl-'] { margin-top: -82px; }
.has-arrows.arrow-center.offset-v-111 .owl-theme .owl-nav [class*='owl-'] { margin-top: -111px; }

.has-arrows.arrow-center.offset-h-21 .owl-theme .owl-nav [class*='owl-'] { left: -21px; }
.has-arrows.arrow-center.offset-h-21 .owl-theme .owl-nav .owl-next { left: auto; right: -21px; }

/* Arrows Top */
.has-arrows.arrow-top .owl-theme .owl-nav [class*='owl-'] { right: 44px; }
.has-arrows.arrow-top .owl-theme .owl-nav .owl-next { right: 0; }
.has-arrows.arrow-top.arrow75 .owl-theme .owl-nav [class*='owl-'] { top: -75px; }

/* Arrows Style 1*/
.owl-theme .owl-nav [class*="owl-"] { text-align: center; background-color: #ffc30c; color: #fff;  width: 40px; height: 40px; font-size: 0; display: inline-block; position: relative; -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; }
.owl-theme .owl-nav [class*="owl-"]:after { font-size: 20px; line-height: 40px; content: "\f3d2"; font-family: "Ionicons"; position: absolute; left: 0; top: 0; text-indent: 0; width: 100%; height: 100%; text-align: center; display: inline-block;}
.owl-theme .owl-nav .owl-next:after { content: "\f3d3"; }
.owl-theme .owl-nav [class*="owl-"]:hover { background-color: #3e3e3e; }
.owl-theme .owl-nav [class*="owl-"].disabled:hover,
.owl-theme .owl-nav [class*="owl-"].disabled { background-color: rgba(255,255,255,0.2); color: #fff; cursor: default; }

/* Arrows Style 2*/
.arrow-style-2 .owl-theme .owl-nav [class*="owl-"].disabled { background-color: #e7e7e7; color: #bdbdbd; }

/* Arrows & Bullets Circle */
.arrow-circle .owl-theme .owl-nav [class*="owl-"],
.bullet-circle .owl-theme .owl-dots .owl-dot span { border-radius: 50%; }

@media only screen and (max-width: 1199px) {
	.has-arrows.arrow-center.offset-v-82 .owl-theme .owl-nav [class*='owl-'] { margin-top: -150px; }
}

@media only screen and (max-width: 767px) {
	.has-arrows .owl-theme .owl-nav, .has-bullets .owl-theme .owl-dots { display: none ; }
}
/* Carousel thumb
-------------------------------------------------------------- */
.owl-theme .owl-thumbs { display: none; }
.has-thumb .owl-theme .owl-thumbs { display: block; }
.has-thumb .owl-thumbs .owl-thumb-item { display: inline-block; background-color: transparent; padding: 0px; margin: 20px 0px 0px 20px; }
.has-thumb .owl-thumbs .owl-thumb-item:first-child { margin-left: 0px; }

.has-thumb.w185 .owl-thumbs .owl-thumb-item { width: 184px; height: 106px; border: 1px solid transparent; }
.has-thumb.w185 .owl-thumbs .owl-thumb-item.active { border-color: #ffc30c; }

@media only screen and (max-width: 1199px) {
	.has-thumb .owl-thumbs .owl-thumb-item:last-child { margin: 20px 0px 0px 0px; }
}

@media only screen and (max-width: 479px) {
	.has-thumb .owl-thumbs .owl-thumb-item { margin: 20px 0px 0px 0px; }
}

/* Parallax
-------------------------------------------------------------- */
.parallax { width: 100%; background-attachment: fixed; background-position: 50% 0; background-repeat: repeat;}
.parallax-1 { background-image: url('../img/parallax/parallax-1.jpg');  }
.parallax-2 { background-image: url('../img/parallax/parallax-2.jpg') ; }
.parallax-3 { background-image: url('../img/parallax/parallax-3.jpg');  }
.parallax-4 { background-image: url('../img/parallax/parallax-4.jpg') ; }

.parallax-overlay { position: relative; z-index: 1; }
.parallax-overlay .bg-parallax-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; }
.parallax-overlay .bg-parallax-overlay.overlay-black { background-color: rgba(40,42,48,0.8); }
.parallax-overlay .bg-parallax-overlay.overlay-black.style2 { background-color: rgba(0,0,0,0.7); }
.parallax-overlay .bg-parallax-overlay.overlay-black.style3 { background-color: rgba(0,0,0,0.8); }

/* Project
-------------------------------------------------------------- */
.themesflat-project .thumb { position: relative; }
.themesflat-project .heading { margin: 0px; }
.themesflat-project .elm-link { z-index: 2; }
.themesflat-project .elm-link > a { display: inline-block !important; margin: 0 4px; }
.themesflat-project .elm-meta > span { display: inline-block; position: relative; }

.themesflat-project.has-margin.mg15 { margin-left: -15px; 	margin-right: -15px;}
.themesflat-project.has-margin.mg15 .project-item { padding-left: 15px;padding-right: 15px;width: 33.33%; float: left; margin-bottom: 30px; }
.themesflat-project.has-margin.mg15.w25 .project-item { width: 25%; }

/* Style 1 */
.themesflat-project.style-1 .text-wrap { position: absolute; top: 50%; left: 50%; transform: translate(-50%, 6%); width: 100%; height: 100%;  z-index: 2; margin-left: 3px; opacity: 0; visibility: hidden; -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease;  }
.themesflat-project.style-1 .heading { font-size: 18px; line-height: 27px; color: #fff; }
.themesflat-project.style-1 .elm-meta > span { padding: 0px 11px; }
.themesflat-project.style-1 .elm-meta > span:before { content: ""; position: absolute; top: 50%; left: 0; transform: translateY(-50%); width: 1px; height: 10px; background-color: #bdbdbd;}
.themesflat-project.style-1 .elm-meta > span:first-child:before { background-color: transparent; width: 0; height: 0; } 
.themesflat-project.style-1 .elm-meta a { color: #ffc30c; line-height: 26px;  }

.themesflat-project.style-1 .thumb:hover .text-wrap {  opacity: 1; visibility: visible; } 
.themesflat-project.style-1 .elm-meta a:hover { color: #fff; }

/* Style 2 */
.themesflat-project.style-2 .text-wrap { background-color: #f7f7f7; padding: 17px; }
.themesflat-project.style-2 .heading { font-size: 14px; line-height: 27px; }
.themesflat-project.style-2 .elm-meta > span { padding: 0px 8px 0px 14px; }
.themesflat-project.style-2 .elm-meta > span:first-child { padding-left: 0px; }
.themesflat-project.style-2 .elm-meta > span:before { content: ""; position: absolute; top: 50%; left: 0; transform: translateY(-50%); width: 1px; height: 10px; background-color: #e0e0e0;}
.themesflat-project.style-2 .elm-meta > span:first-child:before { background-color: transparent; width: 0; height: 0; } 
.themesflat-project.style-2 .elm-meta a { color: #7a7a7a; line-height: 20px;  }

.themesflat-project.style-2 .elm-meta a:hover { color: #ffc30c; }

@media only screen and (max-width: 1199px) {
	.themesflat-project.style-1 .heading { font-size: 12px; }
	.themesflat-project.has-margin.mg15.w25 .project-item { width: 33.33%; }
  .themesflat-project.style-2 .text-wrap{ padding: 15px 5px; }
}

@media only screen and (max-width: 767px) {
	.themesflat-project.style-1 .heading { font-size: 14px; }
	.themesflat-project.has-margin.mg15.w25 .project-item ,
	.themesflat-project.has-margin.mg15 .project-item { width: 50%; }
}

@media only screen and (max-width: 479px) {
	.themesflat-project.has-margin.mg15.w25 .project-item,
	.themesflat-project.has-margin.mg15 .project-item { width: 100%; }
}

/* Testimonials
-------------------------------------------------------------- */
.themesflat-testimonials blockquote { padding: 0px; }
.themesflat-testimonials .name-pos .name  { margin: 0; }
.themesflat-testimonials.max-width-70 blockquote { max-width: 70%; margin: 0 auto; }

/* Center */
.themesflat-testimonials.align-center { text-align: center; }
.themesflat-testimonials.align-center .icon-wrap ,
.themesflat-testimonials.align-center .thumb,
.themesflat-testimonials.align-center .sep { margin: 0 auto; }

.themesflat-testimonials .text p { margin: 0px; }
.themesflat-testimonials .list-star i { color: #ffc30c; letter-spacing: -0.5px; }

/* Has width */
.themesflat-testimonials.has-width.w100 .icon-wrap { width: 100px; height: 100px; line-height: 100px; font-size: 32px; color: #f6c01c; background-color: transparent; }
.themesflat-testimonials.has-width.w35 .icon-wrap { width: 35px; height: 35px;  line-height: 35px; font-size: 18px; color: #fff; background-color: #ffc30c; display: inline-block; }

/* Circle */
.themesflat-testimonials.circle .icon-wrap { border-radius: 50%; }

/* Border  */
.themesflat-testimonials.border-solid .icon-wrap { border: 2px solid #606168; }

/* Style 1 */
.themesflat-testimonials.style-1 .text { margin-top: 41px; }
.themesflat-testimonials.style-1 .text p { font-size: 16px; line-height: 38px;  color: #fefefe; font-style: italic; }
.themesflat-testimonials.style-1 .sep { margin-top: 16px; }
.themesflat-testimonials.style-1 .name { font-size: 16px; line-height: 32px; color: #fff; margin: 33px 0px 0px 0px; }

/* Style 2 */
.themesflat-testimonials.style-2 .testimonial-item { padding-top: 45px; }
.themesflat-testimonials.style-2 .inner { position: relative; }
.themesflat-testimonials.style-2 .thumb { width: 90px; height: 90px; position: absolute; top: 0; left: 50%; transform: translate(-50%,-50%); z-index: 99; }
.themesflat-testimonials.style-2 .thumb img { border-radius: 50%; }
.themesflat-testimonials.style-2  .text { background-color: #fff; padding: 69px 30px 30px 30px; font-style: normal; }
.themesflat-testimonials.style-2 .name-pos .name { font-size: 16px; line-height: 16px; font-weight: 500; }
.themesflat-testimonials.style-2 .name-pos .position { font-family: 'Montserrat', sans-serif; font-size: 14px; line-height: 26px; color: #999; font-weight: 300; display: block; margin: 5px 5px 0px 0px; }
.themesflat-testimonials.style-2 .text p { line-height: 26px; color: #777; margin-top: 7px; }
.themesflat-testimonials.style-2 .list-star { margin-top: 18px; }

/* Style 3 */
.themesflat-testimonials.style-3 { background-color: #f7f7f7;  }
.themesflat-testimonials.style-3 .inner { padding: 42px 20px 33px 20px; }
.themesflat-testimonials.style-3 .thumb { width: 100px; height: 100px; position: relative; }
.themesflat-testimonials.style-3 .icon-wrap { position: absolute; right: 0; bottom: 0; }
.themesflat-testimonials.style-3 .text { font-style: normal; }
.themesflat-testimonials.style-3 p { color: #777; margin-top: 25px; }
.themesflat-testimonials.style-3 .name-pos .name { font-size: 16px; line-height: 26px;  margin-top: 24px; }
.themesflat-testimonials.style-3 .name-pos .position { color: #999; }

/* Quote
-------------------------------------------------------------- */
.themesflat-quote .inner { width: 100%; display: table; overflow: hidden; }
.themesflat-quote .heading-wrap { display: table-cell; vertical-align: middle; margin: 0; text-align: left; width: 75%; }
.themesflat-quote .button-wrap { display: table-cell; vertical-align: middle; margin: 0; text-align: right; width: 25%; }

/* Style 1 */
.themesflat-quote.style-1 .heading { margin: 0; font-size: 24px; color: #fff; }

@media only screen and (max-width: 991px) {
	.themesflat-quote .heading-wrap { width: 60%; }
	.themesflat-quote .button-wrap { width: 40%; }
}

@media only screen and (max-width: 767px) {
	.themesflat-testimonials.max-width-70 blockquote { max-width: 100%; }
}

@media only screen and (max-width: 479px) {
	.themesflat-quote .heading-wrap ,
	.themesflat-quote .button-wrap { display: block; width: 100%; text-align: left; }
	.themesflat-quote .button-wrap { margin-top: 15px; }
  .themesflat-quote.style-1 .heading { font-size: 18px; }
}

/* Tabs
-------------------------------------------------------------- */
.themesflat-tabs .tab-title { margin: 0 ; }
.themesflat-tabs .tab-title .item-title { font-family: "Montserrat", sans-serif;   padding: 0; list-style: none; display: inline-block; letter-spacing: 0.5px;  }
.themesflat-tabs .tab-title .item-title.active { position: relative;  }
.themesflat-tabs .tab-title .item-title > span { cursor: pointer;  transition: none;  display: inline-block; text-transform: uppercase; }

.themesflat-tabs.title-w170 .tab-title { width: 170px; }
/* Style 1 */
.themesflat-tabs.style-1 .tab-title  { margin:0 0 5px 0; background-color: transparent; }
.themesflat-tabs.style-1 .tab-title .item-title  { font-size: 14px; font-weight: 600; color: #777; margin: 0px 5px 0px 0px; border: 1px solid #f2f2f2; }
.themesflat-tabs.style-1 .tab-title .item-title > span { padding: 10px 36px 10px 39px; }
.themesflat-tabs.style-1 .tab-title .item-title.active > span { padding: 13px 36px 10px 39px; }
.themesflat-tabs.style-1 .tab-title .item-title.active { background-color: #fafafb; color: #2b323a;  }
.themesflat-tabs.style-1 .tab-content .item-content { padding: 19px 0px 19px 21px; background-color: #fafafb;  }
.themesflat-tabs.style-1 .tab-content .item-content p { margin: 0px 0px 8px 0px; line-height: 26px; color: #777; letter-spacing: -0.13px; }

.themesflat-tabs.style-1 .tab-content ul { margin: 0px; }
.themesflat-tabs.style-1 .tab-content ul li { padding: 5.5px 7px; }
.themesflat-tabs.style-1 .tab-content ul span { display: block; }
.themesflat-tabs.style-1 .tab-content ul span.fa { text-align: center; width: 30px; height: 30px; background-color: #ffc30c; color: #fff; font-size: 16px; float: left; margin-right: 11px; line-height: 30px; }
.themesflat-tabs.style-1 .tab-content ul span.text { overflow: hidden; }

/* Style 2 */
.themesflat-tabs.style-2 { display: table; width: 100%; }
.themesflat-tabs.style-2 .tab-content-wrap ,
.themesflat-tabs.style-2 .tab-title { display: table-cell; vertical-align: top; }
.themesflat-tabs.style-2 .tab-title .item-title  { font-size: 14px; font-weight: 500; color: #777; margin: 0px 0px 5px 0px; background-color: #f1f1f1; display: block;}
.themesflat-tabs.style-2 .tab-title .item-title > span { padding: 13px 0px; display: block; text-align: center;}
.themesflat-tabs.style-2 .tab-content{ margin-left: 9px; }
.themesflat-tabs.style-2 .tab-content .item-content { padding: 20px 0px 19px 26px; background-color: transparent; border: 1px solid #f2f2f2;  }
.themesflat-tabs.style-2 .tab-content .item-content p { margin-top: 10px; letter-spacing: 0.07px; }
.themesflat-tabs.style-2 .tab-content .item-content p:first-child { margin: 0px; letter-spacing: 0px; }
.themesflat-tabs.style-2 .tab-title .item-title.active { background-color: #ffc30c; color: #222; }

/* Has padding */
.themesflat-tabs.w168 .tab-title .item-title { width: 168px; text-align: center;  }
.themesflat-tabs.w168 .tab-title .item-title > span { padding: 10px 0px; }
.themesflat-tabs.w168 .tab-title .item-title.active > span { padding: 13px 0px 10px 0px; }


.themesflat-tabs.style-1 .tab-title .item-title:hover > span { color: #2b323a; }

@media only screen and (max-width: 1199px) {
	.themesflat-tabs.w168 .tab-title .item-title { width: 140px; }
	.themesflat-tabs.style-1 .tab-title .item-title.active > span,
	.themesflat-tabs.style-1 .tab-title .item-title > span { padding: 10px; }
}

@media only screen and (max-width: 991px) {
	.themesflat-tabs.w168 .tab-title .item-title { width: 100%; }
	.themesflat-tabs.style-1 .tab-title { margin: 0px; }
	.themesflat-tabs.style-1 .tab-title .item-title { margin: 0px 0px 5px 0px; display: block; }
	.themesflat-tabs.style-1 .tab-title .item-title { text-align: center; }
	.themesflat-tabs.style-1 .tab-title .item-title.active > span { padding: 10px 0px;  width: 100%; }
  .themesflat-tabs.style-2 .tab-content .item-content { padding: 15px; }
}

@media only screen and (max-width: 479px) {
	.themesflat-tabs.style-2 .tab-content-wrap, 
	.themesflat-tabs.style-2 .tab-title { display: block; text-align: left; }
	.themesflat-tabs.title-w170 .tab-title { width: 100%; }
	.themesflat-tabs.style-2 .tab-content { margin: 0px; }
}

/* Accordions
-------------------------------------------------------------- */
.themesflat-accordions .accordion-item { margin-bottom: 10px; }
.themesflat-accordions.style-4 .accordion-item { margin-bottom: 6px; }
.themesflat-accordions .accordion-item:last-child { margin-bottom: 0px; }
.themesflat-accordions .accordion-item .accordion-heading { position: relative;  margin-bottom: 0; cursor: pointer; position: relative; }
.themesflat-accordions .accordion-item .accordion-heading > .inner { display: block; position: relative; }

.themesflat-accordions .accordion-item .accordion-content { display: none;  }

/* Has icon */
.themesflat-accordions.has-icon .accordion-item .accordion-heading > .inner:before { content: "";  position: absolute; top: 50%; left: 0; transform: translateY(-50%);  -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; }

.themesflat-accordions.has-icon.icon-left .accordion-item .accordion-heading > .inner { padding: 0px 0px 0px 34px; }
.themesflat-accordions.has-icon.icon-left.iconstyle-2 .accordion-item .accordion-heading > .inner { padding: 0px 0px 0px 64px; }

.themesflat-accordions.has-icon.iconstyle-1 .accordion-item .accordion-heading > .inner:before { content: "\e906"; font-family: "autoraicon" ; font-size: 20px; }
.themesflat-accordions.has-icon.iconstyle-2 .accordion-item .accordion-heading > .inner:before { content: "\f067"; font-family: "FontAwesome"; color: #999; font-size: 18px; width: 49px; height: 49px; text-align: center; line-height: 49px; background-color: transparent; border-right: 1px solid #f2f2f2; }

.themesflat-accordions.has-icon.iconstyle-1 .accordion-item.active .accordion-heading > .inner:before { color: #ffc30c;  }
.themesflat-accordions.has-icon.iconstyle-2 .accordion-item.active .accordion-heading > .inner:before { content: "\f068"; color: #fff; background-color: #ffc30c; border-color: #ffc30c; }

.themesflat-accordions.has-icon.icon-right .accordion-item .accordion-heading > .inner:before { left: auto; right: 0; }

/* Style 1 */
.themesflat-accordions.style-1 .accordion-item { padding: 13px 0px; border: 1px solid #f2f2f2; background-color: transparent; }
.themesflat-accordions.style-1 .accordion-item.active {  padding: 22px 0px 20px 0px; }
.themesflat-accordions.style-1 .accordion-item .accordion-heading { font-size: 14px; color: #777; text-transform: uppercase; padding: 0px 22px; }
.themesflat-accordions.style-1 .accordion-item .accordion-content { padding-left: 56px; padding-top: 13px; line-height: 26px; letter-spacing: -0.07px; }
.themesflat-accordions.style-1 .accordion-item.active  { background-color: #fafafb;   }
.themesflat-accordions.style-1 .accordion-item.active  .accordion-heading { color: #222; }

/* Style 2 */
.themesflat-accordions.style-2 .accordion-item .accordion-heading { font-size: 14px; color: #777; text-transform: uppercase; padding: 0px 0px; border: 1px solid #f2f2f2;  background-color: transparent;}

.themesflat-accordions.style-2 .accordion-item  .accordion-heading {  padding: 13px 0px 14px 0px;  }
.themesflat-accordions.style-2 .accordion-item.active  .accordion-heading { color: #222;  }
.themesflat-accordions.style-2 .accordion-content { padding: 11px 0px 6px 0px; letter-spacing: 0.1px; line-height: 26px; }

/* Style 3 */
.themesflat-accordions.style-3 .accordion-item .accordion-heading { font-size: 16px; color: #777; font-weight: 500; border: 1px solid #f2f2f2;  background-color: transparent; font-weight: 500; text-transform: uppercase; padding: 11px 0px; }
.themesflat-accordions.style-3 .accordion-item.active  .accordion-heading { background-color: #fafafb; color: #222; font-weight: 600; }
.themesflat-accordions.style-3.has-icon.icon-left.iconstyle-2 .accordion-item .accordion-heading > .inner { padding: 0px 0px 0px 67px; }
.themesflat-accordions.style-3 .accordion-content { background-color: #fafafb; padding: 6px 10px 8px 19px; font-weight: 300; color: #777; margin-top: 4px; border: 1px solid #f2f2f2; line-height: 26px; letter-spacing: 0.1px; }

/* Style 4 */
.themesflat-accordions.style-4 .accordion-item .accordion-heading { font-size: 16px; color: #777; font-weight: 500; border: 1px solid #f2f2f2; background-color: #fafafb; text-transform: uppercase;  padding: 13px 23px;}
.themesflat-accordions.style-4 .accordion-item.active  .accordion-heading { background-color: #ffc30c; border-color: #ffc30c; color: #fff; }
.themesflat-accordions.style-4 .accordion-content { border: 1px solid #f2f2f2; padding: 22px 5px 22px 24px;  }
.themesflat-accordions.style-4.has-icon.icon-right .accordion-item .accordion-heading > .inner:before { color: #999; font-size: 30px; right: 0px; }
.themesflat-accordions.style-4.has-icon.icon-right .accordion-item.active .accordion-heading > .inner:before { color: #fff; }

.themesflat-accordions.style-4 .accordion-content .name { font-size: 14px; line-height: 24px; color: #555; margin-bottom: 10px; }
.themesflat-accordions.style-4 .accordion-content p { margin: 0px; }

.themesflat-accordions .accordion-item .accordion-heading:hover { color: #222;  }

@media only screen and (max-width: 991px) {
  .themesflat-accordions.style-2 .accordion-item .accordion-heading { font-size: 13px; padding: 6px 0px; }
}

@media only screen and (max-width: 767px) {
  .themesflat-accordions.style-2 .accordion-item .accordion-heading { padding: 15px 0px; }
}

@media only screen and (max-width: 479px) {
  .themesflat-accordions.style-2 .accordion-item .accordion-heading { padding: 6px 0px; }
}

/* Partner
-------------------------------------------------------------- */
.themesflat-partner.align-center { text-align: center; }
.themesflat-partner.align-center .thumb { margin: 0 auto; }
.themesflat-partner .thumb img { -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; }
.themesflat-partner .partner-color { display: none !important; }
.themesflat-partner:hover .partner-default { display: none !important;  }
.themesflat-partner:hover .partner-color { display: block !important; }
/* Style 1 */
.themesflat-partner.style-1 .inner { border: 1px solid #f2f2f2; padding: 23px 0px 15px 0px; cursor: pointer; -webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease; transition: all 0.3s ease; }
.themesflat-partner.style-1 .thumb { width: 188px; height: 100px;  }
/* Style 2 */
.themesflat-partner.style-2 .thumb {width: 188px; height: 100px; }
.themesflat-partner.style-2 .inner { padding: 15px 0px; }

/* Progress Bar
-------------------------------------------------------------- */
.themesflat-progress { position: relative; overflow: hidden; }
.themesflat-progress .title,
.themesflat-progress .perc { line-height: normal; margin: 0; }
.themesflat-progress .title { position: absolute; left: 0; top: 0; }
.themesflat-progress .progress-animate { width: 0; }
.themesflat-progress .progress-bg { width: 100%; }
.themesflat-progress .perc { width: 0; text-align: right; filter: alpha(opacity=0); opacity: 0; -webkit-transition: opacity 1s ease-in-out; -moz-transition: opacity 1s ease-in-out; transition: opacity 1s ease-in-out; }
.themesflat-progress .perc.show { filter: alpha(opacity=100); opacity: 1; }

.themesflat-progress.height-10px .progress-animate { height: 10px; }

/* Style 1 */
.themesflat-progress.style-1 .title { font-size: 14px; font-weight: 400; letter-spacing: 0.6px; }
.themesflat-progress.style-1 .perc-wrap { font-family: 'Montserrat', sans-serif; color: #35383e; font-size: 14px; letter-spacing: 0.6px; }
.themesflat-progress.style-1 .progress-bg { background-color: #f7f7f7; margin-top: 13px; }
.themesflat-progress.style-1 .progress-animate { background-color: #ffc30c; }

/* Request
-------------------------------------------------------------- */
.themesflat-request-box.style-1 .inner { display: table; width: 100%; overflow: hidden; }
.themesflat-request-box.style-1 .themesflat-headings { display: table-cell; vertical-align: top; width: 37%; text-align: left; }
.themesflat-request-box.style-1 .themesflat-contact-form { display: table-cell; vertical-align: top; width: 56%; text-align: right; }

@media only screen and (max-width: 991px) {
	.themesflat-request-box.style-1 .themesflat-headings,
	.themesflat-request-box.style-1 .themesflat-contact-form { display: block; width: 100%; text-align: left; }
	.themesflat-request-box.style-1 .themesflat-contact-form { margin-top: 15px; }
}
/* Contact Form 7
-------------------------------------------------------------- */
.themesflat-contact-form .wpcf7-form-control-wrap { position: relative; display: block; }

.themesflat-contact-form .wpcf7-form-control-wrap.your-name,
.themesflat-contact-form .wpcf7-form-control-wrap.your-email,
.themesflat-contact-form .wpcf7-form-control-wrap.your-phone { margin-bottom: 14px; }

/* Style 1 */
.themesflat-contact-form.style-1 input[type="text"],
.themesflat-contact-form.style-1 input[type="email"] { height: 46px; border-color: #fff; background: #fff; padding: 9px 19px; }
.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-name,
.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-email,
.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-phone { width: 49%; float: left; }
.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-name ,
.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-email{ margin-right: 2%; }
.themesflat-contact-form.style-1 .wrap-submit { float: left; }

/* Style 2 */
.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-name,
.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-email,
.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-phone,
.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-subject {  width: 49%; float: left; margin-bottom: 20px; }
.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-name ,
.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-email { margin-right: 2%; }
.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-message { clear: both; }
.themesflat-contact-form.style-2 input[type="text"],
.themesflat-contact-form.style-2 input[type="email"],
.themesflat-contact-form.style-2 textarea { background-color: #fafafb; font-weight: 300; }
.themesflat-contact-form.style-2 textarea { height: 188px; }
.themesflat-contact-form.style-2 input[type="submit"] { padding: 19px 49px; margin-top: 20px; }

.themesflat-contact-form.w100 .wpcf7-form-control-wrap.your-name,
.themesflat-contact-form.w100 .wpcf7-form-control-wrap.your-email,
.themesflat-contact-form.w100 .wpcf7-form-control-wrap.your-phone { width: 100%; float: none; margin-right: 0; margin-bottom: 9px; }
.themesflat-contact-form.w100 textarea { height: 142px; }
.themesflat-contact-form.w100 input[type="submit"] { margin-top: 9px; }

.themesflat-contact-form input[type="text"]:focus,
.themesflat-contact-form input[type="email"]:focus { border-color: #ffc30c; }

@media only screen and (max-width: 479px) {
	.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-name,
	.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-email,
	.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-phone,
	.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-name,
	.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-email,
	.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-phone,
	.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-subject { width: 100%; }

	.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-name ,
	.themesflat-contact-form.style-1 .wpcf7-form-control-wrap.your-email,
	.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-name ,
	.themesflat-contact-form.style-2 .wpcf7-form-control-wrap.your-email { margin-right: 0px; }

  .themesflat-contact-form.style-1 .wrap-submit { float: none; }
}

/* Filter
-------------------------------------------------------------- */
.themesflat-filter { margin: 0px; }
.themesflat-filter li { display: inline-block; position: relative; padding: 0px; }

/* Absolute */
.themesflat-filter.filter-absolute { position: absolute; top: 50%; right: 0; transform: translateY(-50%); margin: 1px -3px 0px 0px; }
.themesflat-filter.filter-absolute.style-1 li a { margin: 0px 19px; }
/* Style 1 */
.themesflat-filter.style-1 li a { font-family: 'Montserrat', sans-serif; font-size: 14px; line-height: 28px; font-weight: 500; margin:0px 19.5px; color: #555; }
.themesflat-filter.style-1 li:last-child a { margin-right: 0px; }
.themesflat-filter.style-1 li:first-child a { margin-left: 0px; }
.themesflat-filter.style-1 li:before { content: ""; width: 1px; height: 10px; background-color: #d3d3d3; position: absolute; top: 50%; transform: translateY(-50%); left: 0; }
.themesflat-filter.style-1 li:first-child:before { width: 0; height: 0; background-color: transparent; } 

.themesflat-filter.style-1 li a:hover,
.themesflat-filter.style-1 li.active a { color: #ffc30c; }

@media only screen and (max-width: 1199px) {
	.themesflat-filter.filter-absolute.style-1 li a,
	.themesflat-filter.style-1 li a { margin: 0px 10px; }
}

@media only screen and (max-width: 991px) {
	.themesflat-filter.filter-absolute { position: relative; top: 0; transform: translateY(0%);  margin: 0;}
}

@media only screen and (max-width: 479px) {
	.themesflat-filter li { padding: 5px 0px;}
	.themesflat-filter.style-1 li:before { width: 0px; height: 0px; background-color: transparent; }
}

/* Maps
-------------------------------------------------------------- */
.themesflat-map { height: 245px; }

/* Style 2 */
.themesflat-map.style-2 { height: 388px; }

/* Pricing
-------------------------------------------------------------- */
.row-price-table { width: 100%; }
.row-price-table.col4 > .themesflat-price-table { width: 25%; float: left; border-top: 1px solid #f2f2f2; border-bottom: 1px solid #f2f2f2; border-right: 1px solid #f2f2f2; }
.row-price-table.col4 > .themesflat-price-table:first-child { border-left: 1px solid #f2f2f2; }
.themesflat-price-table .title { margin: 0px; }
.themesflat-price-table ul { margin: 0px; }
.themesflat-price-table .sep.has-width.accent-bg { background-color: #ffc30c; } 
.themesflat-price-table .sep.has-width.w80 { width: 80px; height: 2px; }
.themesflat-price-table .price-table-price .price-wrap { position: relative; }
.themesflat-price-table .price-table-price .price-wrap  span { font-family: 'Montserrat', sans-serif; font-weight: 500; display: inline-block; }
.themesflat-price-table .tag { display: inline-block;  font-family: 'Montserrat', sans-serif;  }

/* Style 1 */
.themesflat-price-table.style-1 { margin-top: 25px; }
.themesflat-price-table.style-1 .inner { padding: 43px 28px; }
.themesflat-price-table.style-1 .title { font-size: 18px; line-height: 24px; }
.themesflat-price-table.style-1 .tag { background-color: #ffc30c; color: #363636; font-size: 12px; padding: 4px 16px; letter-spacing: 0.4px; margin: 12px 0px 6px 3px; font-weight: 500;  }
.themesflat-price-table.style-1 .sep { margin-top: 29px; }
.themesflat-price-table.style-1 .price-table-price { margin-top: 16px; }
.themesflat-price-table.style-1 .price-table-price .figure { font-size: 60px; line-height: 60px; color: #ffc30c; }
.themesflat-price-table.style-1 .price-table-price .currency-month { position: absolute; bottom: 2px; right: 26px; }
.themesflat-price-table.style-1 .price-table-price .currency { color: #ffc30c; }
.themesflat-price-table.style-1 .price-table-price .currency-month > span { position: relative; padding: 0px 16px; font-size: 18px; line-height: 24px;}
.themesflat-price-table.style-1 .price-table-price .currency-month > span:before { content: ""; position: absolute; left: 0; margin-left: -2px; top: 50%; width: 2px; height: 12px; background-color: #999; transform: translateY(-50%); }
.themesflat-price-table.style-1 .price-table-price .currency-month > span:first-child:before { width: 0; height: 0; background-color: transparent; }
.themesflat-price-table.style-1 .price-table-price .currency-month > span:first-child { padding-left: 0px; }
.themesflat-price-table.style-1 .price-table-features { margin-top: 13px; }
.themesflat-price-table.style-1 .price-table-features li { position: relative; line-height: 32px; }
.themesflat-price-table.style-1 .price-table-features li span { padding-left: 34px; }
.themesflat-price-table.style-1 .price-table-features li > i { position: absolute; top: 50%; left: 2px; transform: translateY(-50%); font-size: 8px; color: #cacaca; }

.themesflat-price-table.style-1 .price-table-features li.linethrough span { text-decoration: line-through; color: #b0b0b0; }
.themesflat-price-table.style-1 .price-table-features li.linethrough > i { text-decoration: line-through; color: #ebebeb; }
.themesflat-price-table.style-1 .price-table-button { margin-top: 23px; }
.themesflat-price-table.style-1 .price-table-button > a { padding: 11px 37px; }

.themesflat-price-table.active { margin-top: 0px;} 
.themesflat-price-table.active { background-color: #363636; border-color: #363636; }
.themesflat-price-table.active .price-table-price .month ,
.themesflat-price-table.active .price-table-features li span,
.themesflat-price-table.active .title { color: #fff; }

.themesflat-price-table.style-1 .tag:hover { background-color: #212121; color: #fff; }

@media only screen and (max-width: 1199px) {
	.themesflat-price-table.style-1 .price-table-price .currency-month { position: relative; bottom: 0; right: 0; }
  .themesflat-price-table.style-1 .inner { padding: 15px; }
}

@media only screen and (max-width: 991px)  {
	.row-price-table.col4 > .themesflat-price-table { width: 50%; }
}

@media only screen and (max-width: 479px)  {
	.row-price-table.col4 > .themesflat-price-table { width: 100%; }
	.themesflat-price-table.active { margin-top: 25px; }
}

/* Counter
-------------------------------------------------------------- */
.themesflat-counter .number-wrap { font-family: 'Montserrat', sans-serif; font-weight: 700; }
.themesflat-counter .heading { margin: 0px; }

/* Center */
.themesflat-counter.align-center .text-wrap { text-align: center; }

/* Style 1 */
.themesflat-counter.style-1 .number-wrap { color: #fff; font-size: 52px; line-height: 54px; }
.themesflat-counter.style-1 .suffix { color: #ffc30c; }
.themesflat-counter.style-1 .heading {  font-size: 14px; line-height: 32px; color: #ebebeb; }

/* Team
-------------------------------------------------------------- */
.themesflat-team .thumb { position: relative; z-index: 1; }
.themesflat-team .socials { margin: 0px; }
.themesflat-team .socials li { display: inline-block; padding: 0px; margin: 0px 4px; }
.themesflat-team .socials li a { display: block; text-align: center; }
.themesflat-team .text-wrap .name { margin: 0px; }

/* Center */
.themesflat-team.align-center .text-wrap { text-align: center;  }

/* Style 1 */
.themesflat-team.style-1 .socials { position: absolute; text-align: center; width: 100%; height: 100%; left:0; top: 50%; z-index: 2; }
.themesflat-team.style-1 .socials li { opacity: 0; visibility: hidden; -webkit-transform: translate3d(0,-50%,0); transform: translate3d(0,-50%,0); -webkit-transition: opacity 0.2s, -webkit-transform 0.35s; transition: opacity 0.2s, transform 0.35s; }
.themesflat-team.style-1 .socials li a { width: 38px; height: 38px; line-height: 38px; color: #999; background-color: #fff; transform: translateY(-50%); }
.themesflat-team.style-1 .text-wrap { padding: 16px 0px; }
.themesflat-team.style-1 .text-wrap .name { font-size: 16px; line-height: 26px; letter-spacing: 0.5px; text-transform: uppercase; }
.themesflat-team.style-1 .text-wrap .position { letter-spacing: 0.5px; color: #999; }

.themesflat-team.style-1 .socials li a:hover { background-color: #ffc30c; color: #fff; }
.themesflat-team.style-1 .team-item:hover .socials li { opacity: 1; visibility: visible; -webkit-transform: translate3d(0,0,0); transform: translate3d(0,0,0); }
.themesflat-team.style-1 .team-item:hover .socials li:nth-child(3) { -webkit-transition-delay: 0.15s; transition-delay: 0.25s; }
.themesflat-team.style-1 .team-item:hover .socials li:nth-child(2) { -webkit-transition-delay: 0.1s; transition-delay: 0.15s; }
.themesflat-team.style-1 .team-item:hover .socials li:first-child { -webkit-transition-delay: 0.05s; transition-delay: 0.05s; }

/* Content wrap
-------------------------------------------------------------- */
.separator-solid .flat-content-wrap { border-bottom: 1px solid #f2f2f2; padding-bottom: 22px; margin-bottom: 18px; }
.separator-solid .flat-content-wrap:last-child { border-bottom: 0px;  padding-bottom: 0px; margin: 0px;}
.flat-content-wrap .title { font-size: 18px; line-height: 36px; color: #222; font-weight: 600; letter-spacing: -0.15px; }
.flat-content-wrap p { color: #777; letter-spacing: -0.15px; margin: 0px; }
.flat-content-wrap .item { width: 50%; float: left; }
.flat-content-wrap .item .pd35 { padding: 20px 35px 18px 32px; }

.flat-content-wrap .sep.has-width.w60 { width: 60px; height: 2px; }
.flat-content-wrap .sep.has-width.accent-bg { background-color: #ffc30c; }

.flat-content-wrap.pd26 { padding-left: 26px; }

/* Style 2 */
.flat-content-wrap.style-2 .title { font-size: 16px; line-height: 28px; margin: 0px; }
.flat-content-wrap.style-2 p { color: #555; letter-spacing: 0px; margin-top: 9px; }

/* Style 3 */
.flat-content-wrap.style-3 .title { font-size: 22px; line-height: 28px; margin: 0px; }
.flat-content-wrap.style-3 p { color: #777; letter-spacing: 0px; margin-top: 24px; }

@media only screen and (max-width: 1199px) {
  .flat-content-wrap.pd26 { padding-left: 10px; }
}

@media only screen and (max-width: 767px) {
  .flat-content-wrap.pd26 { padding-left: 0px; }
}

@media only screen and (max-width: 479px) {
	.flat-content-wrap .item { width: 100%; float: none; }
}
/* Gallery
-------------------------------------------------------------- */
.themesflat-gallery .thumb { position: relative; }
.themesflat-gallery .text-wrap span { font-family: 'Montserrat', sans-serif; text-transform: uppercase;  }

/* Style 1 */
.themesflat-gallery.style-1 .text-wrap { position: absolute; top: 50%; left: 39px; transform:  translateY(-50%); background-color: rgba(0,0,0,0.5); padding: 22px 44px 25px 32px; }
.themesflat-gallery.style-1 .text-wrap span { font-size: 22px; line-height: 35px; font-weight: 300; color: #fff; display: block;}
.themesflat-gallery.style-1 .text-wrap .heading { font-size: 28px; line-height: 35px; font-weight: 700; color: #fff; text-transform: uppercase; margin: 0px; }
.themesflat-gallery.style-1 a { padding: 11px 38px; margin-top: 21px; }

@media only screen and (max-width: 479px) {
	.themesflat-gallery.style-1 .text-wrap { display: none; }
}

/* Project detail
-------------------------------------------------------------- */
.detail-inner-wrap { width: 100%; }
.detail-inner-wrap .detail-info { float: left; margin-right: 35.5px; width: 28.7%; }
.detail-inner-wrap .detail-gallery { overflow: hidden; }

.detail-inner-wrap .list-info { margin: 15px 0px 0px 0px; }
.detail-inner-wrap .list-info  li { padding: 12.5px 0px; position: relative; border-top: 1px solid #f2f2f2;}
.detail-inner-wrap .list-info  li:first-child { border-top: 0px; }
.detail-inner-wrap .list-info span { color: #777; }
.detail-inner-wrap .list-info span > a { color: #777; font-style :italic; }
.detail-inner-wrap .list-info span.text { color: #222; }
.detail-inner-wrap .list-info.has-icon.icon-left .icon{ position: absolute; top: 50%; transform: translateY(-50%); left: 2px; }
.detail-inner-wrap .list-info.has-icon.icon-left span.text { padding-left: 30px; }
.detail-inner-wrap .list-info.has-icon.icon-left span.right { position: absolute; left: 169px; }

.detail-inner-wrap .list-info span > a:hover { color: #ffc30c; }

@media only screen and (max-width: 991px) {
	.detail-inner-wrap .detail-info { width: 100%; float: none; margin: 0px; }
	.detail-inner-wrap .list-info.has-icon.icon-left span.right { left: 80%; }
}

@media only screen and (max-width: 479px) {
	.detail-inner-wrap .list-info.has-icon.icon-left span.right { left: 50%; }
}

/* Lines
-------------------------------------------------------------- */
.themesflat-lines .line { z-index: 1; display: block; }
.themesflat-lines .line-full .line { width: 100%; }

.themesflat-lines.style-1 .line { height: 1px; background-color: #f2f2f2; }



