# Database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': 'geodjango_db',
        'USER': 'geodjango',
        'PASSWORD': 'password',
        'HOST': 'db',
        'PORT': '5432',
    }
}

# CORS settings
INSTALLED_APPS = [
    # other apps...
    'corsheaders',
]

MIDDLEWARE = [
    # other middleware...
    'corsheaders.middleware.CorsMiddleware',
]

# Allow all origins for CORS (adjust this based on your needs)
CORS_ALLOW_ALL_ORIGINS = True
