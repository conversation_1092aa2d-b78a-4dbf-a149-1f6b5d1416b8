FROM python:3.9

# Install dependencies and correct version of libgdal
RUN apt-get update && apt-get install -y \
    libpq-dev \
    postgresql-client \
    binutils \
    gdal-bin \
    python3-gdal \
    build-essential \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install GDAL 3.10.2 manually
RUN wget https://github.com/OSGeo/gdal/releases/download/v3.10.2/gdal-3.10.2.tar.gz \
    && tar -xzf gdal-3.10.2.tar.gz \
    && cd gdal-3.10.2 \
    && ./configure \
    && make \
    && make install \
    && cd .. \
    && rm -rf gdal-3.10.2 gdal-3.10.2.tar.gz

# Install Python dependencies
WORKDIR /code
COPY requirements.txt /code/
RUN pip install -r requirements.txt

# Copy the current directory contents into the container at /code
COPY . /code/

# Expose port 8000
EXPOSE 8000

# Set environment variables
ENV PYTHONUNBUFFERED 1
