version: '3'

services:
  db:
    image: postgis/postgis:latest
    container_name: geodjango_db
    environment:
      POSTGRES_USER: geodjango
      POSTGRES_PASSWORD: password
      POSTGRES_DB: geodjango_db
    ports:
      - "5432:5432"
    volumes:
      - pg_data:/var/lib/postgresql/data

  web:
    build: .
    container_name: geodjango_web
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/code
    ports:
      - "8000:8000"
    depends_on:
      - db

volumes:
  pg_data:
